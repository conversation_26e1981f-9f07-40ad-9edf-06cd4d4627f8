import React from 'react';
import { Droppable } from 'react-beautiful-dnd';
import KanbanTask from './KanbanTask';

const KanbanColumn = ({ column, tasks, onEditTask }) => {
  return (
    <div className="kanban-column">
      <h3 className="column-title">
        {column.title} <span className="task-count">{tasks.length}</span>
      </h3>
      <Droppable droppableId={column.id}>
        {(provided, snapshot) => (
          <div
            className={`task-list ${snapshot.isDraggingOver ? 'dragging-over' : ''}`}
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {tasks.map((task, index) => (
              <KanbanTask 
                key={task.id} 
                task={task} 
                index={index} 
                onEdit={() => onEditTask(task.id)} 
              />
            ))}
            {provided.placeholder}
            {tasks.length === 0 && (
              <div className="empty-column">
                <p>No tasks</p>
              </div>
            )}
          </div>
        )}
      </Droppable>
    </div>
  );
};

export default KanbanColumn;
