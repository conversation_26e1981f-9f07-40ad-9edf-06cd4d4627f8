import { test, expect } from '@playwright/test';

// Test configuration
const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');
  
  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  
  const needsAuth = await page.locator('input[type="email"]').isVisible();
  
  if (needsAuth) {
    console.log('📝 Filling in credentials...');
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
    if (stillNeedsAuth) {
      throw new Error('Authentication failed');
    }
    
    console.log('✅ Authentication successful');
    return true;
  }
  
  console.log('✅ Already authenticated');
  return true;
}

// Helper function to take screenshot with detailed info
async function captureEvidence(page, testName, step, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `functional-${testName}-${step}-${timestamp}.png`;
  await page.screenshot({ path: `test-results/${filename}`, fullPage: true });
  console.log(`📸 ${description} - Evidence: ${filename}`);
  return filename;
}

test.describe('Functional Verification Audit', () => {
  let authWorking = false;

  test.beforeEach(async ({ page }) => {
    try {
      authWorking = await authenticate(page);
    } catch (error) {
      console.log('❌ Authentication failed:', error.message);
      authWorking = false;
    }
  });

  test('1. Profile and Logout Functionality Test', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await captureEvidence(page, 'profile-logout', 'initial', 'Dashboard loaded');
    
    // Look for profile button (based on screenshot, it's in top right)
    const profileSelectors = [
      'button:has-text("the")', // Based on screenshot showing "the" in top right
      '[data-testid="user-menu"]',
      '.user-menu',
      'button[aria-label*="profile"]',
      'button[aria-label*="user"]'
    ];
    
    let profileButtonFound = false;
    let profileButton = null;
    
    for (const selector of profileSelectors) {
      const element = page.locator(selector);
      if (await element.count() > 0) {
        profileButtonFound = true;
        profileButton = element;
        console.log(`✅ Profile button found: ${selector}`);
        break;
      }
    }
    
    if (!profileButtonFound) {
      console.log('❌ FAIL: Profile button not found');
      await captureEvidence(page, 'profile-logout', 'no-profile-button', 'Profile button not found');
      expect(profileButtonFound).toBe(true);
      return;
    }
    
    // Click profile button
    try {
      await profileButton.click();
      await page.waitForTimeout(1000);
      await captureEvidence(page, 'profile-logout', 'profile-clicked', 'After clicking profile button');
      
      // Look for logout option
      const logoutSelectors = [
        'text="Logout"',
        'text="Sign Out"',
        'text="Log Out"',
        'button:has-text("Logout")',
        'button:has-text("Sign Out")',
        '[data-testid="logout"]'
      ];
      
      let logoutFound = false;
      let logoutButton = null;
      
      for (const selector of logoutSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.isVisible()) {
          logoutFound = true;
          logoutButton = element;
          console.log(`✅ Logout button found: ${selector}`);
          break;
        }
      }
      
      if (!logoutFound) {
        console.log('❌ FAIL: Logout button not found after clicking profile');
        await captureEvidence(page, 'profile-logout', 'no-logout-button', 'Logout button not found');
        expect(logoutFound).toBe(true);
        return;
      }
      
      // Test logout functionality
      console.log('🔄 Testing logout functionality...');
      await logoutButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      await captureEvidence(page, 'profile-logout', 'after-logout', 'After clicking logout');
      
      // Check if we're back to login page
      const backToLogin = await page.locator('input[type="email"]').isVisible();
      
      if (backToLogin) {
        console.log('✅ PASS: Logout functionality works - redirected to login');
      } else {
        console.log('❌ FAIL: Logout button exists but does not function - still authenticated');
        await captureEvidence(page, 'profile-logout', 'logout-failed', 'Logout failed - still authenticated');
        expect(backToLogin).toBe(true);
      }
      
    } catch (error) {
      console.log(`❌ FAIL: Error testing profile/logout: ${error.message}`);
      await captureEvidence(page, 'profile-logout', 'error', 'Error during profile/logout test');
      expect(false).toBe(true);
    }
  });

  test('2. Project Creation Wizard Functionality Test', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(`${PRODUCTION_URL}/start`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await captureEvidence(page, 'project-wizard', 'start-page', 'Start page loaded');
    
    // Look for "Start Project Wizard" button (visible in screenshot)
    const wizardSelectors = [
      'text="Start Project Wizard"',
      'button:has-text("Start Project Wizard")',
      'text="Create Your First Project"',
      '[data-testid="project-wizard"]'
    ];
    
    let wizardButtonFound = false;
    let wizardButton = null;
    
    for (const selector of wizardSelectors) {
      const element = page.locator(selector);
      if (await element.count() > 0 && await element.isVisible()) {
        wizardButtonFound = true;
        wizardButton = element;
        console.log(`✅ Project wizard button found: ${selector}`);
        break;
      }
    }
    
    if (!wizardButtonFound) {
      console.log('❌ FAIL: Project wizard button not found');
      await captureEvidence(page, 'project-wizard', 'no-wizard-button', 'Project wizard button not found');
      expect(wizardButtonFound).toBe(true);
      return;
    }
    
    // Click the wizard button
    try {
      console.log('🔄 Testing project wizard functionality...');
      await wizardButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      await captureEvidence(page, 'project-wizard', 'after-click', 'After clicking project wizard');
      
      // Check if we navigated to a project creation form/page
      const currentUrl = page.url();
      console.log(`📍 Current URL after wizard click: ${currentUrl}`);
      
      // Look for project creation form elements
      const formSelectors = [
        'input[placeholder*="project"]',
        'input[placeholder*="name"]',
        'input[placeholder*="title"]',
        'textarea[placeholder*="description"]',
        'form',
        'text="Project Name"',
        'text="Project Title"',
        'text="Description"'
      ];
      
      let formFound = false;
      for (const selector of formSelectors) {
        if (await page.locator(selector).count() > 0) {
          formFound = true;
          console.log(`✅ Project creation form element found: ${selector}`);
          break;
        }
      }
      
      if (formFound) {
        console.log('✅ PASS: Project wizard button works - navigated to creation form');
      } else {
        console.log('❌ FAIL: Project wizard button exists but does not lead to functional form');
        await captureEvidence(page, 'project-wizard', 'no-form', 'No project creation form found');
        expect(formFound).toBe(true);
      }
      
    } catch (error) {
      console.log(`❌ FAIL: Error testing project wizard: ${error.message}`);
      await captureEvidence(page, 'project-wizard', 'error', 'Error during project wizard test');
      expect(false).toBe(true);
    }
  });

  test('3. Navigation Functionality Test', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const navigationTests = [
      { name: 'Start', expectedPath: '/start' },
      { name: 'Track', expectedPath: '/track' },
      { name: 'Earn', expectedPath: '/earn' }
    ];
    
    for (const navTest of navigationTests) {
      console.log(`🔄 Testing ${navTest.name} navigation...`);
      
      // Find and click navigation button
      const navButton = page.locator(`text="${navTest.name}"`).first();
      
      if (await navButton.count() === 0) {
        console.log(`❌ FAIL: ${navTest.name} button not found`);
        await captureEvidence(page, 'navigation', `${navTest.name.toLowerCase()}-not-found`, `${navTest.name} button not found`);
        expect(await navButton.count()).toBeGreaterThan(0);
        continue;
      }
      
      try {
        await navButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const currentUrl = page.url();
        console.log(`📍 After clicking ${navTest.name}: ${currentUrl}`);
        
        await captureEvidence(page, 'navigation', `${navTest.name.toLowerCase()}-page`, `${navTest.name} page loaded`);
        
        if (currentUrl.includes(navTest.expectedPath)) {
          console.log(`✅ PASS: ${navTest.name} navigation works - correct URL`);
        } else {
          console.log(`❌ FAIL: ${navTest.name} navigation failed - wrong URL`);
          expect(currentUrl).toContain(navTest.expectedPath);
        }
        
      } catch (error) {
        console.log(`❌ FAIL: Error clicking ${navTest.name}: ${error.message}`);
        await captureEvidence(page, 'navigation', `${navTest.name.toLowerCase()}-error`, `Error clicking ${navTest.name}`);
        expect(false).toBe(true);
      }
    }
  });

  test('4. Mobile Navigation Functionality Test', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await captureEvidence(page, 'mobile-nav', 'initial', 'Mobile view loaded');
    
    // Check if main navigation buttons are visible
    const navButtons = ['Start', 'Track', 'Earn'];
    let visibleButtons = 0;
    
    for (const buttonText of navButtons) {
      const button = page.locator(`text="${buttonText}"`).first();
      const isVisible = await button.isVisible();
      
      if (isVisible) {
        visibleButtons++;
        console.log(`✅ ${buttonText} button visible on mobile`);
        
        // Test if button is clickable
        try {
          await button.click();
          await page.waitForTimeout(1000);
          console.log(`✅ ${buttonText} button clickable on mobile`);
        } catch (error) {
          console.log(`❌ ${buttonText} button visible but not clickable: ${error.message}`);
        }
      } else {
        console.log(`❌ ${buttonText} button not visible on mobile`);
      }
    }
    
    // Look for mobile menu button
    const mobileMenuSelectors = [
      'button[aria-label*="menu"]',
      'button[aria-label*="Menu"]',
      '.hamburger',
      '[data-testid="mobile-menu"]',
      'button:has-text("☰")'
    ];
    
    let mobileMenuFound = false;
    for (const selector of mobileMenuSelectors) {
      if (await page.locator(selector).isVisible()) {
        mobileMenuFound = true;
        console.log(`✅ Mobile menu button found: ${selector}`);
        break;
      }
    }
    
    if (visibleButtons === 0 && !mobileMenuFound) {
      console.log('❌ FAIL: No mobile navigation found - buttons not visible and no mobile menu');
      await captureEvidence(page, 'mobile-nav', 'no-navigation', 'No mobile navigation found');
      expect(visibleButtons > 0 || mobileMenuFound).toBe(true);
    } else {
      console.log(`✅ PASS: Mobile navigation available (${visibleButtons} visible buttons, mobile menu: ${mobileMenuFound})`);
    }
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('5. Studio/Team Page Functionality Test', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(`${PRODUCTION_URL}/studios`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await captureEvidence(page, 'studios', 'page-loaded', 'Studios page loaded');
    
    // Check if page loaded successfully (not 404)
    const pageTitle = await page.title();
    const bodyText = await page.locator('body').textContent();
    
    if (bodyText.includes('404') || bodyText.includes('Not Found')) {
      console.log('❌ FAIL: Studios page returns 404');
      expect(false).toBe(true);
      return;
    }
    
    // Look for studio functionality
    const studioElements = [
      'text="Create Studio"',
      'text="New Studio"',
      'button:has-text("Create")',
      'text="Studio"',
      'text="Team"'
    ];
    
    let functionalityFound = false;
    for (const selector of studioElements) {
      if (await page.locator(selector).count() > 0) {
        functionalityFound = true;
        console.log(`✅ Studio functionality element found: ${selector}`);
        break;
      }
    }
    
    if (functionalityFound) {
      console.log('✅ PASS: Studios page loads with functionality');
    } else {
      console.log('❌ FAIL: Studios page loads but shows no functionality');
      await captureEvidence(page, 'studios', 'no-functionality', 'Studios page with no functionality');
      expect(functionalityFound).toBe(true);
    }
  });
});
