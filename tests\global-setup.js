// Global setup for Playwright tests
import { chromium } from '@playwright/test';

async function globalSetup(config) {
  console.log('🚀 Starting Royaltea Platform Test Suite');
  console.log('📊 Setting up test environment...');

  // Get base URL from config
  const baseURL = config.use?.baseURL || process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173';
  console.log(`🌐 Using base URL: ${baseURL}`);

  // Create a browser instance for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the development server to be ready
    console.log('⏳ Waiting for development server...');
    await page.goto(baseURL, { waitUntil: 'networkidle' });
    console.log('✅ Development server is ready');

    // Basic environment check - just verify the app loads
    console.log('🔧 Performing basic environment check...');

    // Check if the main app elements are present
    const hasBody = await page.locator('body').count() > 0;
    const hasReact = await page.evaluate(() => window.React !== undefined).catch(() => false);

    console.log(`📱 App loaded: ${hasBody ? '✅' : '❌'}`);
    console.log(`⚛️ React available: ${hasReact ? '✅' : '❌'}`);

    if (!hasBody) {
      throw new Error('Application failed to load properly');
    }
    
    // Store authentication state if needed
    // This can be used to skip login in tests
    await context.storageState({ path: 'tests/auth-state.json' });
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('✅ Global setup completed successfully');
}

export default globalSetup;
