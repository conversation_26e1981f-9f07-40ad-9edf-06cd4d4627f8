// Static Roadmap Edge Function
export default async (request, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };
  
  // Handle OPTIONS request for CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }
  
  try {
    // Hardcoded roadmap data
    const roadmapData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          },
          {
            id: "1.2",
            title: "User Authentication",
            tasks: [
              { id: "1.2.1", text: "Implement email/password authentication", completed: true },
              { id: "1.2.2", text: "Add social login options (GitHub, Google)", completed: false },
              { id: "1.2.3", text: "Create user profile management", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true },
              { id: "2.1.4", text: "Create royalty model configuration", completed: true },
              { id: "2.1.5", text: "Implement revenue tranches setup", completed: false }
            ]
          },
          {
            id: "2.2",
            title: "Project Dashboard",
            tasks: [
              { id: "2.2.1", text: "Create project overview page", completed: true },
              { id: "2.2.2", text: "Implement project settings", completed: false },
              { id: "2.2.3", text: "Add team management interface", completed: false }
            ]
          }
        ]
      },
      {
        id: 3,
        title: "Contribution Tracking System",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "3.1",
            title: "Manual Contribution Entry",
            tasks: [
              { id: "3.1.1", text: "Design contribution entry forms", completed: true },
              { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
              { id: "3.1.3", text: "Add task selection from configured types", completed: true },
              { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
              { id: "3.1.5", text: "Create contribution description field", completed: true },
              { id: "3.1.6", text: "Add date range selection", completed: true },
              { id: "3.1.7", text: "Implement file/asset attachment", completed: false }
            ]
          },
          {
            id: "3.2",
            title: "Contribution Approval",
            tasks: [
              { id: "3.2.1", text: "Create contribution review interface", completed: false },
              { id: "3.2.2", text: "Implement approval workflow", completed: false },
              { id: "3.2.3", text: "Add commenting and feedback system", completed: false }
            ]
          }
        ]
      },
      {
        id: 4,
        title: "Royalty Calculation & Distribution",
        timeframe: "Phase 3",
        expanded: false,
        sections: [
          {
            id: "4.1",
            title: "Royalty Calculation Engine",
            tasks: [
              { id: "4.1.1", text: "Implement CoG model algorithm", completed: false },
              { id: "4.1.2", text: "Create revenue tranche processing", completed: false },
              { id: "4.1.3", text: "Add support for multiple calculation models", completed: false }
            ]
          },
          {
            id: "4.2",
            title: "Payment Processing",
            tasks: [
              { id: "4.2.1", text: "Integrate payment gateway", completed: false },
              { id: "4.2.2", text: "Implement payment scheduling", completed: false },
              { id: "4.2.3", text: "Create payment history and reporting", completed: false }
            ]
          }
        ]
      }
    ];
    
    // Calculate stats
    const stats = calculateStats(roadmapData);
    
    // Return the data
    return new Response(
      JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats,
        source: 'static-edge-function'
      }),
      { headers }
    );
  } catch (error) {
    // Return error response
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        stack: error.stack
      }),
      { 
        status: 500,
        headers
      }
    );
  }
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;
    
    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });
    
    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;
    
    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}
