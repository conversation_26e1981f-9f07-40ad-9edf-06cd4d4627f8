import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { Card, CardBody, CardHeader, Button, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Dashboard Overview Section
 *
 * Central hub showing key metrics, recent activity, and quick stats.
 * Part of the Home canvas in the experimental navigation system.
 */
const DashboardOverview = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeContributions: 0,
    totalRevenue: 0,
    pendingValidations: 0
  });
  const [loading, setLoading] = useState(true);

  // Fetch dashboard statistics
  useEffect(() => {
    const fetchDashboardStats = async () => {
      if (!currentUser) return;

      try {
        // Try to fetch projects count (using created_by instead of owner_id)
        let projectsCount = 0;
        try {
          const { count } = await supabase
            .from('projects')
            .select('*', { count: 'exact', head: true })
            .eq('created_by', currentUser.id);
          projectsCount = count || 0;
        } catch (error) {
          if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
            console.warn('Projects table access issue (this is normal if database is not set up yet)');
          } else {
            console.error('Error fetching projects count:', error);
          }
        }

        // Try to fetch active contributions count
        let contributionsCount = 0;
        try {
          const { count } = await supabase
            .from('contributions')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', currentUser.id)
            .eq('status', 'approved');
          contributionsCount = count || 0;
        } catch (error) {
          if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
            console.warn('Contributions table access issue (this is normal if database is not set up yet)');
          } else {
            console.error('Error fetching contributions count:', error);
          }
        }

        // Try to fetch total revenue (through project_contributors relationship)
        let totalRevenue = 0;
        try {
          const { data: revenueData } = await supabase
            .from('revenue_entries')
            .select(`
              amount,
              project_id,
              projects!inner(
                project_contributors!inner(user_id)
              )
            `)
            .eq('projects.project_contributors.user_id', currentUser.id);
          totalRevenue = revenueData?.reduce((sum, entry) => sum + (entry.amount || 0), 0) || 0;
        } catch (error) {
          if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
            console.warn('Revenue entries table access issue (this is normal if database is not set up yet)');
          } else {
            console.error('Error fetching revenue data:', error);
          }
        }

        // Try to fetch pending validations count
        let validationsCount = 0;
        try {
          const { count } = await supabase
            .from('contributions')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', currentUser.id)
            .eq('status', 'pending');
          validationsCount = count || 0;
        } catch (error) {
          if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
            console.warn('Contributions table access issue for pending validations (this is normal if database is not set up yet)');
          } else {
            console.error('Error fetching pending validations count:', error);
          }
        }

        setStats({
          totalProjects: projectsCount,
          activeContributions: contributionsCount,
          totalRevenue: totalRevenue,
          pendingValidations: validationsCount
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, [currentUser]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Stat cards data
  const statCards = [
    {
      title: 'Active Projects',
      value: stats.totalProjects,
      icon: '📁',
      color: 'from-blue-500 to-cyan-500',
      description: 'Projects you own or contribute to'
    },
    {
      title: 'Contributions',
      value: stats.activeContributions,
      icon: '⏱️',
      color: 'from-green-500 to-emerald-500',
      description: 'Approved contributions this month'
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      icon: '💰',
      color: 'from-yellow-500 to-orange-500',
      description: 'Lifetime earnings from projects'
    },
    {
      title: 'Pending Reviews',
      value: stats.pendingValidations,
      icon: '⏳',
      color: 'from-purple-500 to-pink-500',
      description: 'Contributions awaiting validation'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <h1 className="text-4xl font-bold text-white mb-2">
          Welcome back, {currentUser?.user_metadata?.full_name || currentUser?.email}! 👋
        </h1>
        <p className="text-white/70 text-lg">
          Here's what's happening with your projects today
        </p>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.1 * index }}
            whileHover={{ scale: 1.05 }}
            className="h-full"
          >
            <Card className="h-full bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardBody className="p-6">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${stat.color} flex items-center justify-center mb-4`}>
                  <span className="text-2xl">{stat.icon}</span>
                </div>
                <h3 className="text-white/90 text-sm font-medium mb-1">{stat.title}</h3>
                <p className="text-white text-2xl font-bold mb-2">{stat.value}</p>
                <p className="text-white/60 text-xs">{stat.description}</p>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <h2 className="text-xl font-bold text-white">Quick Actions</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
                size="lg"
                startContent={<span>🚀</span>}
                onClick={() => navigate('/start')}
              >
                Start New Project
              </Button>
              <Button
                className="bg-gradient-to-r from-green-500 to-emerald-600 text-white"
                size="lg"
                startContent={<span>⏱️</span>}
                onClick={() => navigate('/track')}
              >
                Track Contribution
              </Button>
              <Button
                className="bg-gradient-to-r from-orange-500 to-red-600 text-white"
                size="lg"
                startContent={<span>📊</span>}
                onClick={() => navigate('/analytics')}
              >
                View Analytics
              </Button>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Status Indicators */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="flex flex-wrap gap-2 justify-center"
      >
        <Chip color="success" variant="flat">
          System Online
        </Chip>
        <Chip color="primary" variant="flat">
          {stats.totalProjects} Active Projects
        </Chip>
        <Chip color="warning" variant="flat">
          {stats.pendingValidations} Pending Reviews
        </Chip>
      </motion.div>
    </div>
  );
};

export default DashboardOverview;
