import { test, expect } from '@playwright/test';

/**
 * Dashboard Action Cards Test Suite
 * 
 * Tests all dashboard action cards to ensure they navigate correctly
 * and load the expected pages without errors.
 */

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate using the immersive auth system
async function authenticate(page) {
  console.log('🔐 Starting authentication...');

  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);

  // Check if we're already authenticated (dashboard visible)
  const newProject = await page.locator('text="New Project"').isVisible().catch(() => false);
  const browseProjects = await page.locator('text="Browse Projects"').isVisible().catch(() => false);
  const isDashboard = newProject && browseProjects && page.url().includes('/dashboard');
  if (isDashboard) {
    console.log('✅ Already authenticated - on dashboard');
    return true;
  }

  // Step 1: Click "Sign In" to go to login page
  const hasSignInButton = await page.locator('text="Sign In"').isVisible().catch(() => false);
  if (hasSignInButton) {
    console.log('🔘 Clicking Sign In button...');
    await page.click('text="Sign In"');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  }

  // Step 2: Click "LOGIN" to reveal the login form
  const hasLoginButton = await page.locator('text="LOGIN"').isVisible().catch(() => false);
  if (hasLoginButton) {
    console.log('🔘 Clicking LOGIN button...');
    await page.click('text="LOGIN"');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  }

  // Step 3: Find and fill the login form inputs
  const emailInput = page.locator('input[placeholder*="@"]').first();
  const passwordInput = page.locator('input[placeholder*="password"]').first();

  // Verify inputs are visible
  const emailVisible = await emailInput.isVisible().catch(() => false);
  const passwordVisible = await passwordInput.isVisible().catch(() => false);

  if (!emailVisible || !passwordVisible) {
    console.log(`❌ Login form inputs not found - email: ${emailVisible}, password: ${passwordVisible}`);
    return false;
  }

  console.log('📧 Found email input with placeholder containing "@"');
  console.log('🔒 Found password input with placeholder containing "password"');

  // Fill login form
  console.log('📝 Filling login form...');
  await emailInput.fill(TEST_CREDENTIALS.email);
  await passwordInput.fill(TEST_CREDENTIALS.password);

  // Step 4: Click the submit button
  console.log('🔘 Clicking submit button...');
  const submitButton = page.locator('button[type="submit"]').first();
  const submitVisible = await submitButton.isVisible().catch(() => false);

  if (!submitVisible) {
    console.log('❌ Submit button not found');
    return false;
  }

  await submitButton.click();
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);

  // Verify authentication by checking for dashboard elements
  const finalUrl = page.url();

  // Check multiple dashboard indicators (same as debug test)
  const welcomeBack = await page.locator('text="Welcome back"').isVisible().catch(() => false);
  const newProjectCard = await page.locator('text="New Project"').isVisible().catch(() => false);
  const browseProjectsCard = await page.locator('text="Browse Projects"').isVisible().catch(() => false);
  const trackContributionCard = await page.locator('text="Track Contribution"').isVisible().catch(() => false);
  const viewAnalyticsCard = await page.locator('text="View Analytics"').isVisible().catch(() => false);

  // Dashboard is considered loaded if we have the action cards (which the debug test confirmed work)
  const hasDashboard = newProjectCard && browseProjectsCard && trackContributionCard && viewAnalyticsCard;
  const isAuthenticated = hasDashboard && finalUrl.includes('/dashboard');

  console.log(`🔐 Authentication ${isAuthenticated ? 'successful' : 'failed'}: ${finalUrl}`);
  console.log(`📊 Dashboard visible: ${hasDashboard}`);
  console.log(`📊 Action cards - New Project: ${newProjectCard}, Browse: ${browseProjectsCard}, Track: ${trackContributionCard}, Analytics: ${viewAnalyticsCard}`);

  return isAuthenticated;
}

// Helper function to capture evidence
async function captureEvidence(page, category, testName, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `test-results/${category}-${testName}-${timestamp}.png`;
  
  await page.screenshot({ 
    path: filename, 
    fullPage: true 
  });
  
  console.log(`📸 Evidence captured: ${filename} - ${description}`);
  return filename;
}

test.describe('Dashboard Action Cards Tests', () => {
  let authWorking = false;

  test.beforeAll(async ({ browser }) => {
    const page = await browser.newPage();
    authWorking = await authenticate(page);
    await page.close();
  });

  test('0. Authentication and Dashboard Verification', async ({ page }) => {
    console.log('🔍 Testing authentication and dashboard visibility...');

    const isAuthenticated = await authenticate(page);
    expect(isAuthenticated).toBe(true);

    // Navigate to dashboard
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Verify dashboard elements are visible (use action cards since they're confirmed to work)
    const newProjectCard = page.locator('text="New Project"');
    const browseProjectsCard = page.locator('text="Browse Projects"');
    await expect(newProjectCard).toBeVisible({ timeout: 10000 });
    await expect(browseProjectsCard).toBeVisible({ timeout: 10000 });

    // Capture evidence of dashboard
    await captureEvidence(page, 'dashboard', 'authentication-verification', 'Dashboard after authentication');

    // Check for action cards container
    const actionCards = page.locator('.cursor-pointer, [class*="card"]');
    const cardCount = await actionCards.count();
    console.log(`📊 Found ${cardCount} potential action cards`);

    // Log all text content to understand what's on the page
    const pageText = await page.textContent('body');
    console.log('📄 Page contains "New Project":', pageText.includes('New Project'));
    console.log('📄 Page contains "Browse Projects":', pageText.includes('Browse Projects'));
    console.log('📄 Page contains "Track Contribution":', pageText.includes('Track Contribution'));
    console.log('📄 Page contains "View Analytics":', pageText.includes('View Analytics'));

    console.log('✅ Authentication and dashboard verification complete');
  });

  test('1. New Project Button Navigation', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');

    await authenticate(page);

    // Navigate to dashboard
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    console.log('🚀 Testing New Project button...');

    // First verify we're on the dashboard
    const dashboardTitle = page.locator('text="Welcome back"');
    await expect(dashboardTitle).toBeVisible({ timeout: 10000 });

    await captureEvidence(page, 'dashboard', 'before-new-project-click', 'Dashboard before clicking New Project');

    // Find and click the New Project card using more specific selector
    const newProjectCard = page.locator('[data-testid="new-project-card"], .cursor-pointer:has-text("New Project"), button:has-text("New Project")').first();
    await expect(newProjectCard).toBeVisible({ timeout: 10000 });

    await newProjectCard.click();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const currentUrl = page.url();
    console.log(`📍 After New Project click: ${currentUrl}`);

    await captureEvidence(page, 'dashboard', 'after-new-project-click', 'Page after clicking New Project');

    // Verify navigation to /start
    expect(currentUrl).toContain('/start');
    console.log('✅ New Project button navigates correctly to /start');
  });

  test('2. Browse Projects Button Navigation', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');

    await authenticate(page);

    // Navigate to dashboard
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    console.log('📊 Testing Browse Projects button...');

    // First verify we're on the dashboard
    const dashboardTitle = page.locator('text="Welcome back"');
    await expect(dashboardTitle).toBeVisible({ timeout: 10000 });

    await captureEvidence(page, 'dashboard', 'before-browse-projects-click', 'Dashboard before clicking Browse Projects');

    // Find and click the Browse Projects card using more specific selector
    const browseProjectsCard = page.locator('[data-testid="browse-projects-card"], .cursor-pointer:has-text("Browse Projects"), button:has-text("Browse Projects")').first();
    await expect(browseProjectsCard).toBeVisible({ timeout: 10000 });

    await browseProjectsCard.click();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const currentUrl = page.url();
    console.log(`📍 After Browse Projects click: ${currentUrl}`);

    await captureEvidence(page, 'dashboard', 'after-browse-projects-click', 'Page after clicking Browse Projects');

    // Verify navigation to /projects
    expect(currentUrl).toContain('/projects');
    console.log('✅ Browse Projects button navigates correctly to /projects');
  });

  test('3. Track Contribution Button Navigation', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await authenticate(page);
    
    // Navigate to dashboard
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    console.log('⏱️ Testing Track Contribution button...');
    
    // Find and click the Track Contribution card
    const trackContributionCard = page.locator('text="Track Contribution"').first();
    await expect(trackContributionCard).toBeVisible();
    
    await captureEvidence(page, 'dashboard', 'before-track-contribution-click', 'Dashboard before clicking Track Contribution');
    
    await trackContributionCard.click();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const currentUrl = page.url();
    console.log(`📍 After Track Contribution click: ${currentUrl}`);
    
    await captureEvidence(page, 'dashboard', 'after-track-contribution-click', 'Page after clicking Track Contribution');
    
    // Verify navigation to /track
    expect(currentUrl).toContain('/track');
    console.log('✅ Track Contribution button navigates correctly to /track');
  });

  test('4. View Analytics Button Navigation', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await authenticate(page);
    
    // Navigate to dashboard
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    console.log('📈 Testing View Analytics button...');
    
    // Find and click the View Analytics card
    const viewAnalyticsCard = page.locator('text="View Analytics"').first();
    await expect(viewAnalyticsCard).toBeVisible();
    
    await captureEvidence(page, 'dashboard', 'before-view-analytics-click', 'Dashboard before clicking View Analytics');
    
    await viewAnalyticsCard.click();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const currentUrl = page.url();
    console.log(`📍 After View Analytics click: ${currentUrl}`);
    
    await captureEvidence(page, 'dashboard', 'after-view-analytics-click', 'Page after clicking View Analytics');
    
    // Verify navigation to /analytics
    expect(currentUrl).toContain('/analytics');
    console.log('✅ View Analytics button navigates correctly to /analytics');
  });

  test('5. All Dashboard Cards Comprehensive Test', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await authenticate(page);
    
    console.log('🎯 Running comprehensive dashboard cards test...');
    
    const cardTests = [
      { name: 'New Project', expectedPath: '/start', icon: '🚀' },
      { name: 'Browse Projects', expectedPath: '/projects', icon: '📊' },
      { name: 'Track Contribution', expectedPath: '/track', icon: '⏱️' },
      { name: 'View Analytics', expectedPath: '/analytics', icon: '📈' }
    ];
    
    const results = [];
    
    for (const cardTest of cardTests) {
      console.log(`\n${cardTest.icon} Testing ${cardTest.name} card...`);
      
      // Navigate back to dashboard
      await page.goto(PRODUCTION_URL);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      try {
        // Find and click the card
        const card = page.locator(`text="${cardTest.name}"`).first();
        await expect(card).toBeVisible({ timeout: 5000 });
        
        await card.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const currentUrl = page.url();
        const success = currentUrl.includes(cardTest.expectedPath);
        
        results.push({
          card: cardTest.name,
          expected: cardTest.expectedPath,
          actual: currentUrl,
          success: success
        });
        
        console.log(`${success ? '✅' : '❌'} ${cardTest.name}: ${currentUrl}`);
        
      } catch (error) {
        console.log(`❌ ${cardTest.name}: Error - ${error.message}`);
        results.push({
          card: cardTest.name,
          expected: cardTest.expectedPath,
          actual: 'ERROR',
          success: false,
          error: error.message
        });
      }
    }
    
    // Summary
    console.log('\n📋 Dashboard Cards Test Summary:');
    const successCount = results.filter(r => r.success).length;
    console.log(`✅ Successful: ${successCount}/${results.length}`);
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.card}: ${result.actual}`);
    });
    
    // Capture final evidence
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await captureEvidence(page, 'dashboard', 'comprehensive-test-complete', 'Dashboard after comprehensive test');
    
    // Expect all cards to work
    expect(successCount).toBe(cardTests.length);
  });
});
