import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Avatar, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { 
  Play, 
  BarChart3, 
  DollarSign, 
  Bell, 
  Settings, 
  User,
  LogOut,
  ChevronDown
} from 'lucide-react';

/**
 * Modern Navigation Component
 * 
 * Clean, focused navigation with:
 * - Core user journey: Start → Track → Earn
 * - Essential user actions: Notifications, Settings, Profile
 * - Everything else accessible through clever secondary navigation
 */
const ModernNavigation = ({ currentUser, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [notifications] = useState(3); // Mock notification count

  // Core navigation items - the main user journey
  const coreNavItems = [
    {
      id: 'start',
      label: 'Start',
      path: '/start',
      icon: Play,
      description: 'Create projects and get started'
    },
    {
      id: 'track',
      label: 'Track',
      path: '/track',
      icon: BarChart3,
      description: 'Monitor progress and contributions'
    },
    {
      id: 'earn',
      label: 'Earn',
      path: '/earn',
      icon: DollarSign,
      description: 'View earnings and revenue'
    }
  ];

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const handleNavigation = (path) => {
    navigate(path);
  };

  const handleUserAction = (action) => {
    switch (action) {
      case 'profile':
        navigate('/profile');
        break;
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        if (onLogout) {
          onLogout();
        }
        break;
      default:
        break;
    }
  };

  return (
    <nav className="sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          
          {/* Logo/Brand */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-3"
          >
            <button
              onClick={() => navigate('/')}
              className="flex items-center gap-2 text-gray-900 dark:text-white hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
            >
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="font-bold text-xl hidden sm:block">Royaltea</span>
            </button>
          </motion.div>

          {/* Core Navigation - Main User Journey */}
          <div className="hidden md:flex items-center gap-2">
            {coreNavItems.map((item, index) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Button
                    variant={active ? "solid" : "light"}
                    color={active ? "primary" : "default"}
                    onClick={() => handleNavigation(item.path)}
                    className={`
                      ${active 
                        ? 'bg-purple-600 text-white shadow-lg' 
                        : 'text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20'
                      }
                      transition-all duration-200 font-medium
                    `}
                    startContent={<Icon size={18} />}
                  >
                    {item.label}
                  </Button>
                </motion.div>
              );
            })}
          </div>

          {/* Right Side - User Actions */}
          <div className="flex items-center gap-3">
            
            {/* Notifications */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Badge content={notifications} color="danger" size="sm" isInvisible={notifications === 0}>
                <Button
                  isIconOnly
                  variant="light"
                  onClick={() => navigate('/notifications')}
                  className="text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400"
                >
                  <Bell size={20} />
                </Button>
              </Badge>
            </motion.div>

            {/* User Menu */}
            {currentUser ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Dropdown placement="bottom-end">
                  <DropdownTrigger>
                    <Button
                      variant="light"
                      className="p-0 min-w-0 h-auto gap-2"
                    >
                      <Avatar
                        src={currentUser.avatar_url}
                        name={currentUser.display_name || currentUser.email}
                        size="sm"
                        className="cursor-pointer"
                      />
                      <ChevronDown size={16} className="text-gray-500" />
                    </Button>
                  </DropdownTrigger>
                  <DropdownMenu
                    aria-label="User menu"
                    onAction={handleUserAction}
                  >
                    <DropdownItem
                      key="profile"
                      startContent={<User size={16} />}
                      description="View and edit your profile"
                    >
                      Profile
                    </DropdownItem>
                    <DropdownItem
                      key="settings"
                      startContent={<Settings size={16} />}
                      description="Account and app settings"
                    >
                      Settings
                    </DropdownItem>
                    <DropdownItem
                      key="logout"
                      color="danger"
                      startContent={<LogOut size={16} />}
                      description="Sign out of your account"
                    >
                      Logout
                    </DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Button
                  color="primary"
                  onClick={() => navigate('/login')}
                  className="font-medium"
                >
                  Sign In
                </Button>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden border-t border-gray-200 dark:border-gray-800">
        <div className="flex items-center justify-around py-2">
          {coreNavItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path);
            
            return (
              <Button
                key={item.id}
                variant="light"
                isIconOnly
                onClick={() => handleNavigation(item.path)}
                className={`
                  ${active 
                    ? 'text-purple-600 bg-purple-50 dark:bg-purple-900/20' 
                    : 'text-gray-600 dark:text-gray-400'
                  }
                  transition-colors
                `}
              >
                <Icon size={20} />
              </Button>
            );
          })}
        </div>
      </div>
    </nav>
  );
};

export default ModernNavigation;
