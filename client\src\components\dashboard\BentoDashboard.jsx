import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardBody, Button, Progress, Chip, Spinner } from '@heroui/react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../utils/supabase/supabase.utils';
import { useDataSync } from '../../contexts/DataSyncContext';
import { toast } from 'react-hot-toast';
import AnalyticsDataService from '../../services/AnalyticsDataService';

/**
 * Enhanced Bento Grid Dashboard Component
 *
 * Production-ready dashboard with real-time data integration, performance optimization,
 * and comprehensive user analytics. Features dynamic content, animations, and quick actions.
 */
const BentoDashboard = ({ currentUser, displayName }) => {
  const navigate = useNavigate();
  const { syncTriggers } = useDataSync();

  // Real-time dashboard state
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeContributions: 0,
    totalRevenue: 0,
    pendingRoyalties: 0,
    completedTasks: 0,
    activeTeams: 0,
    monthlyGrowth: 0,
    lastUpdated: null
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [realtimeConnected, setRealtimeConnected] = useState(false);
  const [recentActivity, setRecentActivity] = useState([]);

  // Real-time data fetching and synchronization
  const fetchDashboardData = useCallback(async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch comprehensive dashboard data
      const [projectsData, contributionsData, revenueData, activityData] = await Promise.allSettled([
        // Projects count
        supabase
          .from('projects')
          .select('*', { count: 'exact', head: true })
          .eq('created_by', currentUser.id),

        // Active contributions
        supabase
          .from('contributions')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', currentUser.id)
          .eq('status', 'active'),

        // Revenue data from analytics service
        AnalyticsDataService.getAnalyticsOverview(currentUser.id, '30d'),

        // Recent activity - use user_activity_logs table
        supabase
          .from('user_activity_logs')
          .select('*')
          .eq('user_id', currentUser.id)
          .order('timestamp', { ascending: false })
          .limit(5)
      ]);

      // Process results with fallbacks
      const projectsCount = projectsData.status === 'fulfilled' ? (projectsData.value.count || 0) : 0;
      const contributionsCount = contributionsData.status === 'fulfilled' ? (contributionsData.value.count || 0) : 0;
      const analytics = revenueData.status === 'fulfilled' ? revenueData.value : null;
      const activities = activityData.status === 'fulfilled' ? (activityData.value.data || []) : [];

      // Update stats with real data
      setStats({
        totalProjects: projectsCount,
        activeContributions: contributionsCount,
        totalRevenue: analytics?.revenue?.total || 0,
        pendingRoyalties: analytics?.revenue?.pending || 0,
        completedTasks: analytics?.performance?.completedTasks || 0,
        activeTeams: analytics?.teams?.active || 0,
        monthlyGrowth: analytics?.revenue?.growth || 0,
        lastUpdated: new Date().toISOString()
      });

      setRecentActivity(activities);

    } catch (err) {
      console.error('Dashboard data fetch error:', err);
      setError(err.message);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [currentUser]);

  // Initialize dashboard data
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Sync with data changes
  useEffect(() => {
    if (syncTriggers.projects || syncTriggers.contributions || syncTriggers.revenue) {
      fetchDashboardData();
    }
  }, [syncTriggers, fetchDashboardData]);

  // Real-time subscriptions
  useEffect(() => {
    if (!currentUser) return;

    const setupRealtimeSubscriptions = async () => {
      try {
        // Subscribe to projects changes
        const projectsChannel = supabase
          .channel('dashboard-projects')
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'projects',
            filter: `created_by=eq.${currentUser.id}`
          }, () => {
            fetchDashboardData();
          })
          .subscribe();

        // Subscribe to contributions changes
        const contributionsChannel = supabase
          .channel('dashboard-contributions')
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'contributions',
            filter: `user_id=eq.${currentUser.id}`
          }, () => {
            fetchDashboardData();
          })
          .subscribe();

        setRealtimeConnected(true);

        return () => {
          supabase.removeChannel(projectsChannel);
          supabase.removeChannel(contributionsChannel);
        };
      } catch (err) {
        console.error('Realtime setup error:', err);
      }
    };

    const cleanup = setupRealtimeSubscriptions();
    return () => cleanup?.then(fn => fn?.());
  }, [currentUser, fetchDashboardData]);

  // Animation variants for cards - Optimized for faster response
  const cardVariants = useMemo(() => ({
    hidden: { opacity: 0, y: 10, scale: 0.98 }, // Reduced movement for faster animation
    visible: { opacity: 1, y: 0, scale: 1 },
    hover: { scale: 1.01, y: -1 } // Reduced hover effect for better performance
  }), []);

  // Memoized navigation handlers for better performance
  const handleTrackNavigation = useCallback(() => navigate('/track'), [navigate]);
  const handleEarnNavigation = useCallback(() => navigate('/earn'), [navigate]);
  const handleStartNavigation = useCallback(() => navigate('/start'), [navigate]);
  const handleProjectsNavigation = useCallback(() => navigate('/projects'), [navigate]);
  const handleAnalyticsNavigation = useCallback(() => navigate('/analytics'), [navigate]);

  // Quick action cards data - Memoized to prevent re-creation
  const quickActions = useMemo(() => [
    {
      id: 'new-project',
      title: 'New Project',
      description: 'Start your next big idea',
      icon: '🚀',
      color: 'from-green-500 to-emerald-600',
      action: handleStartNavigation
    },
    {
      id: 'browse-projects',
      title: 'Browse Projects',
      description: 'Explore existing projects',
      icon: '📊',
      color: 'from-blue-500 to-cyan-600',
      action: handleProjectsNavigation
    },
    {
      id: 'track-contribution',
      title: 'Track Contribution',
      description: 'Log your work progress',
      icon: '⏱️',
      color: 'from-orange-500 to-red-600',
      action: handleTrackNavigation
    },
    {
      id: 'view-analytics',
      title: 'View Analytics',
      description: 'Check your performance',
      icon: '📈',
      color: 'from-purple-500 to-pink-600',
      action: handleAnalyticsNavigation
    }
  ], [handleStartNavigation, handleTrackNavigation, handleProjectsNavigation, handleAnalyticsNavigation]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8 text-center"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            Welcome back, {displayName}
          </h1>
          <p className="text-lg text-default-600">
            {loading ? 'Loading your dashboard...' : 'Here\'s what\'s happening with your projects today'}
          </p>
          <div className="flex justify-center gap-2 mt-2">
            <Chip color="success" variant="flat">
              ✨ Production Dashboard
            </Chip>
            {realtimeConnected && (
              <Chip color="success" variant="flat">
                🔴 Live Updates
              </Chip>
            )}
            {stats.lastUpdated && (
              <Chip color="default" variant="flat" size="sm">
                Updated {new Date(stats.lastUpdated).toLocaleTimeString()}
              </Chip>
            )}
          </div>
          {error && (
            <Chip color="danger" variant="flat" className="mt-2">
              ⚠️ {error}
            </Chip>
          )}
        </motion.div>

        {/* Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Large Welcome Card */}
          <motion.div
            className="lg:col-span-2 lg:row-span-2"
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            transition={{ duration: 0.2, delay: 0.05 }} // Reduced duration and delay
          >
            <Card className="h-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 text-white">
              <CardBody className="p-8 flex flex-col justify-between">
                <div>
                  <h2 className="text-3xl font-bold mb-4">🎯 Your Dashboard</h2>
                  <p className="text-lg opacity-90 mb-6">
                    Track your creative journey, manage projects, and earn royalties from your contributions.
                  </p>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                      <div className="text-2xl font-bold">
                        {loading ? <Spinner size="sm" color="white" /> : stats.totalProjects}
                      </div>
                      <div className="text-sm opacity-80">Active Projects</div>
                    </div>
                    <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                      <div className="text-2xl font-bold">
                        {loading ? <Spinner size="sm" color="white" /> : stats.activeContributions}
                      </div>
                      <div className="text-sm opacity-80">Contributions</div>
                    </div>
                  </div>
                </div>
                <Button
                  size="lg"
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
                  variant="bordered"
                  onClick={handleTrackNavigation}
                >
                  View All Projects →
                </Button>
              </CardBody>
            </Card>
          </motion.div>

          {/* Revenue Card */}
          <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            transition={{ duration: 0.2, delay: 0.1 }} // Reduced duration and delay
          >
            <Card className="h-full">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Total Revenue</h3>
                  <span className="text-2xl">💰</span>
                </div>
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {loading ? <Spinner size="sm" color="success" /> : `$${stats.totalRevenue.toLocaleString()}`}
                </div>
                <div className="text-sm text-default-600 mb-4">
                  {stats.monthlyGrowth > 0 ? `+${stats.monthlyGrowth}%` : 'No change'} from last month
                </div>
                <Progress
                  value={loading ? 0 : Math.min(100, (stats.totalRevenue / 10000) * 100)}
                  color="success"
                  className="mb-3"
                  size="sm"
                />
                <Button
                  size="sm"
                  color="success"
                  variant="flat"
                  onClick={handleEarnNavigation}
                  className="w-full"
                >
                  View Details
                </Button>
              </CardBody>
            </Card>
          </motion.div>

          {/* Pending Royalties Card */}
          <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            transition={{ duration: 0.2, delay: 0.15 }} // Reduced duration and delay
          >
            <Card className="h-full">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Pending Royalties</h3>
                  <span className="text-2xl">💎</span>
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {loading ? <Spinner size="sm" color="primary" /> : `$${stats.pendingRoyalties.toLocaleString()}`}
                </div>
                <div className="text-sm text-default-600 mb-4">
                  {stats.pendingRoyalties > 0 ? 'Ready for payout' : 'No pending royalties'}
                </div>
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onClick={handleEarnNavigation}
                  className="w-full"
                >
                  Claim Now
                </Button>
              </CardBody>
            </Card>
          </motion.div>

          {/* Quick Actions Grid */}
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              transition={{ duration: 0.2, delay: 0.2 + index * 0.05 }} // Reduced duration and stagger delay
            >
              <Card 
                className="h-full cursor-pointer group"
                onClick={action.action}
              >
                <CardBody className={`p-6 bg-gradient-to-br ${action.color} text-white relative overflow-hidden`}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]" />
                  </div>
                  
                  <div className="relative z-10">
                    <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">
                      {action.icon}
                    </div>
                    <h3 className="text-lg font-bold mb-2">{action.title}</h3>
                    <p className="text-sm opacity-90">{action.description}</p>
                  </div>
                  
                  {/* Hover Effect */}
                  <div className="absolute inset-0 bg-white/0 group-hover:bg-white/10 transition-all duration-300" />
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Recent Activity Section */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold">🔥 Recent Activity</h3>
                {loading && <Spinner size="sm" />}
              </div>
              <div className="space-y-3">
                {loading ? (
                  // Loading skeleton
                  Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-default-100 rounded-lg animate-pulse">
                      <div className="space-y-2">
                        <div className="h-4 bg-default-300 rounded w-32"></div>
                        <div className="h-3 bg-default-200 rounded w-24"></div>
                      </div>
                      <div className="h-3 bg-default-200 rounded w-16"></div>
                    </div>
                  ))
                ) : recentActivity.length > 0 ? (
                  recentActivity.map((activity, index) => (
                    <div key={activity.id || index} className="flex items-center justify-between p-3 bg-default-100 rounded-lg hover:bg-default-200 transition-colors">
                      <div>
                        <div className="font-medium">{activity.action || activity.activity_type}</div>
                        <div className="text-sm text-default-600">{activity.project_name || activity.description}</div>
                      </div>
                      <div className="text-sm text-default-500">
                        {activity.created_at ? new Date(activity.created_at).toLocaleDateString() : 'Recently'}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-default-500">
                    <div className="text-4xl mb-2">📝</div>
                    <div className="font-medium">No recent activity</div>
                    <div className="text-sm">Start working on projects to see activity here</div>
                  </div>
                )}
              </div>
              {!loading && recentActivity.length > 0 && (
                <div className="mt-4 text-center">
                  <Button
                    size="sm"
                    variant="flat"
                    onClick={() => navigate('/analytics')}
                  >
                    View All Activity →
                  </Button>
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default BentoDashboard;
