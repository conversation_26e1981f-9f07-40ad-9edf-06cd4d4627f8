import React from 'react';
import { Draggable } from 'react-beautiful-dnd';

const KanbanTask = ({ task, index, onEdit }) => {
  // Get difficulty color based on difficulty level
  const getDifficultyColor = (level) => {
    switch (level) {
      case 'easy':
        return 'green';
      case 'medium':
        return 'orange';
      case 'hard':
        return 'red';
      case 'expert':
        return 'purple';
      default:
        return 'gray';
    }
  };

  // Format assignee display
  const getAssigneeDisplay = () => {
    if (!task.assignee) return null;

    return (
      <div className="task-assignee">
        {task.assignee.avatar_url ? (
          <img
            src={task.assignee.avatar_url}
            alt={task.assignee.display_name}
            className="assignee-avatar"
          />
        ) : (
          <div className="assignee-initials">
            {task.assignee.display_name?.charAt(0) || '?'}
          </div>
        )}
        <span className="assignee-name">{task.assignee.display_name}</span>
      </div>
    );
  };

  return (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <div
          className={`kanban-task ${snapshot.isDragging ? 'dragging' : ''} ${task.status === 'done' ? 'completed' : ''}`}
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          onClick={onEdit}
        >
          <div className="task-header">
            <h4 className="task-title">{task.title}</h4>
            <div className="task-id">#{task.id.substring(0, 8)}</div>
          </div>

          {task.description && (
            <div className="task-description">
              {task.description.length > 100
                ? `${task.description.substring(0, 100)}...`
                : task.description}
            </div>
          )}

          <div className="task-footer">
            <div className="task-meta">
              {task.task_type && (
                <div className="task-type">{task.task_type}</div>
              )}

              {task.difficulty_level && (
                <div
                  className="task-difficulty"
                  style={{ backgroundColor: getDifficultyColor(task.difficulty_level) }}
                >
                  {task.difficulty_level}
                </div>
              )}

              {task.logged_hours > 0 && (
                <div className="task-hours" title="Logged Hours">
                  <i className="bi bi-clock-history"></i> {task.logged_hours}h
                </div>
              )}

              {task.status === 'done' && (
                <div className="task-completed" title="Completed">
                  <i className="bi bi-check-circle-fill"></i>
                </div>
              )}
            </div>

            {getAssigneeDisplay()}
          </div>
        </div>
      )}
    </Draggable>
  );
};

export default KanbanTask;
