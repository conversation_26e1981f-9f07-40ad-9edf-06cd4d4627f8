import React, { useState, useEffect } from 'react';

const KanbanToolbar = ({ 
  onAddTask, 
  onFilterChange, 
  onSearchChange, 
  contributors, 
  taskTypes, 
  difficultyLevels 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    assignee: '',
    taskType: '',
    difficulty: '',
    showCompleted: true
  });
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearchChange(value);
  };

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Toggle filter panel visibility
  const toggleFilterPanel = () => {
    setIsFilterExpanded(!isFilterExpanded);
  };

  // Clear all filters
  const clearFilters = () => {
    const resetFilters = {
      assignee: '',
      taskType: '',
      difficulty: '',
      showCompleted: true
    };
    setFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  return (
    <div className="kanban-toolbar">
      <div className="toolbar-main">
        <div className="search-container">
          <input
            type="text"
            placeholder="Search tasks..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="search-input"
          />
          {searchTerm && (
            <button 
              className="clear-search" 
              onClick={() => {
                setSearchTerm('');
                onSearchChange('');
              }}
            >
              ×
            </button>
          )}
        </div>
        
        <div className="toolbar-actions">
          <button 
            className={`filter-toggle ${isFilterExpanded ? 'active' : ''}`} 
            onClick={toggleFilterPanel}
          >
            <i className="bi bi-funnel"></i> Filter
          </button>
          
          <button className="add-task-btn" onClick={onAddTask}>
            <i className="bi bi-plus"></i> Add Task
          </button>
        </div>
      </div>
      
      {isFilterExpanded && (
        <div className="filter-panel">
          <div className="filter-group">
            <label>Assignee</label>
            <select 
              value={filters.assignee} 
              onChange={(e) => handleFilterChange('assignee', e.target.value)}
            >
              <option value="">All Assignees</option>
              <option value="unassigned">Unassigned</option>
              {contributors.map(contributor => (
                <option key={contributor.id} value={contributor.id}>
                  {contributor.display_name || contributor.email}
                </option>
              ))}
            </select>
          </div>
          
          <div className="filter-group">
            <label>Task Type</label>
            <select 
              value={filters.taskType} 
              onChange={(e) => handleFilterChange('taskType', e.target.value)}
            >
              <option value="">All Types</option>
              {taskTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="filter-group">
            <label>Difficulty</label>
            <select 
              value={filters.difficulty} 
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
            >
              <option value="">All Difficulties</option>
              {difficultyLevels.map(level => (
                <option key={level.value} value={level.value}>
                  {level.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="filter-group checkbox-group">
            <label>
              <input 
                type="checkbox" 
                checked={filters.showCompleted} 
                onChange={(e) => handleFilterChange('showCompleted', e.target.checked)}
              />
              Show Completed Tasks
            </label>
          </div>
          
          <button className="clear-filters-btn" onClick={clearFilters}>
            Clear Filters
          </button>
        </div>
      )}
      
      {(filters.assignee || filters.taskType || filters.difficulty || !filters.showCompleted) && (
        <div className="active-filters">
          <span className="active-filters-label">Active Filters:</span>
          {filters.assignee && (
            <span className="filter-tag">
              Assignee: {filters.assignee === 'unassigned' ? 'Unassigned' : 
                contributors.find(c => c.id === filters.assignee)?.display_name || 'Unknown'}
              <button onClick={() => handleFilterChange('assignee', '')}>×</button>
            </span>
          )}
          {filters.taskType && (
            <span className="filter-tag">
              Type: {taskTypes.find(t => t.value === filters.taskType)?.name || filters.taskType}
              <button onClick={() => handleFilterChange('taskType', '')}>×</button>
            </span>
          )}
          {filters.difficulty && (
            <span className="filter-tag">
              Difficulty: {difficultyLevels.find(d => d.value === filters.difficulty)?.name || filters.difficulty}
              <button onClick={() => handleFilterChange('difficulty', '')}>×</button>
            </span>
          )}
          {!filters.showCompleted && (
            <span className="filter-tag">
              Hiding Completed Tasks
              <button onClick={() => handleFilterChange('showCompleted', true)}>×</button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default KanbanToolbar;
