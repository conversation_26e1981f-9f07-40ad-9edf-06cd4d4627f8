import React, { useState, useContext, useEffect } from 'react';
import { Card, CardBody, Button, Tabs, Tab, Chip, Progress, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { useNavigate } from 'react-router-dom';
import { useDataSync } from '../../contexts/DataSyncContext';
import SimpleTimeTracker from '../../components/tracking/SimpleTimeTracker';
import QuickContributionForm from '../../components/tracking/QuickContributionForm';
import ContributionProgress from '../../components/tracking/ContributionProgress';
import { Clock, Play, Pause, BarChart3, Target, TrendingUp, Users, Download, Filter, Calendar, Share2 } from 'lucide-react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import AnalyticsDataService from '../../services/AnalyticsDataService';

/**
 * Enhanced TrackPage Component
 *
 * Production-ready time tracking and contribution management page with advanced features:
 * - Advanced time tracking with team collaboration
 * - Contribution analytics and reporting
 * - Export capabilities and project management integration
 * - Real-time team collaboration features
 */
const TrackPage = () => {
  const { currentUser } = useContext(UserContext);
  const { syncTriggers } = useDataSync();
  const navigate = useNavigate();

  // Enhanced state management
  const [activeTab, setActiveTab] = useState('time-tracker');
  const [isTracking, setIsTracking] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [teamMembers, setTeamMembers] = useState([]);
  const [analytics, setAnalytics] = useState({
    todayHours: 0,
    weekHours: 0,
    monthHours: 0,
    productivity: 0,
    teamRanking: 0
  });
  const [loading, setLoading] = useState(true);
  const [projects, setProjects] = useState([]);
  const [filterOptions, setFilterOptions] = useState({
    dateRange: '7d',
    projectFilter: 'all',
    teamFilter: 'all'
  });

  // Load tracking data and team information
  useEffect(() => {
    const loadTrackingData = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Load user's projects - get projects where user is creator or team member
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('id, name, description, created_by')
          .or(`created_by.eq.${currentUser.id},team_id.in.(select team_id from team_members where user_id = '${currentUser.id}')`);

        if (projectsError) throw projectsError;
        setProjects(projectsData || []);

        // Load analytics data
        const analyticsData = await AnalyticsDataService.getAnalyticsOverview(currentUser.id, filterOptions.dateRange);
        setAnalytics({
          todayHours: analyticsData?.performance?.todayHours || 0,
          weekHours: analyticsData?.performance?.weekHours || 0,
          monthHours: analyticsData?.performance?.monthHours || 0,
          productivity: analyticsData?.performance?.score || 0,
          teamRanking: analyticsData?.teams?.ranking || 0
        });

        // Load team members if project is selected
        if (selectedProject) {
          const { data: membersData, error: membersError } = await supabase
            .from('project_contributors')
            .select(`
              user_id,
              role,
              users:user_id (
                id,
                email,
                user_metadata
              )
            `)
            .eq('project_id', selectedProject.id);

          if (membersError) throw membersError;
          setTeamMembers(membersData || []);
        }

      } catch (error) {
        console.error('Error loading tracking data:', error);
        toast.error('Failed to load tracking data');
      } finally {
        setLoading(false);
      }
    };

    loadTrackingData();
  }, [currentUser, selectedProject, filterOptions.dateRange]);

  // Sync with data changes
  useEffect(() => {
    if (syncTriggers.contributions || syncTriggers.projects) {
      // Reload data when contributions or projects change
      const loadData = async () => {
        if (!currentUser) return;

        try {
          const analyticsData = await AnalyticsDataService.getAnalyticsOverview(currentUser.id, filterOptions.dateRange);
          setAnalytics({
            todayHours: analyticsData?.performance?.todayHours || 0,
            weekHours: analyticsData?.performance?.weekHours || 0,
            monthHours: analyticsData?.performance?.monthHours || 0,
            productivity: analyticsData?.performance?.score || 0,
            teamRanking: analyticsData?.teams?.ranking || 0
          });
        } catch (error) {
          console.error('Error syncing tracking data:', error);
        }
      };

      loadData();
    }
  }, [syncTriggers, currentUser, filterOptions.dateRange]);

  const handleStartTracking = () => {
    setIsTracking(true);
  };

  const handleStopTracking = () => {
    setIsTracking(false);
  };

  const handleViewAnalytics = () => {
    navigate('/analytics/contributions');
  };

  const handleViewProjects = () => {
    navigate('/projects');
  };

  const handleViewMissions = () => {
    navigate('/missions');
  };

  const handleExportData = async (format = 'csv') => {
    try {
      const exportData = await AnalyticsDataService.exportAnalyticsData(currentUser.id, {
        dataType: 'contributions',
        format,
        period: filterOptions.dateRange,
        filters: {
          project: selectedProject?.id,
          dateRange: filterOptions.dateRange
        }
      });

      // Create download link
      const blob = new Blob([exportData], { type: format === 'csv' ? 'text/csv' : 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `contributions-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast.success(`Data exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export data');
    }
  };

  const handleShareProgress = async () => {
    try {
      const shareData = {
        title: 'My Contribution Progress',
        text: `I've tracked ${analytics.weekHours} hours this week on Royaltea!`,
        url: window.location.href
      };

      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(`${shareData.text} ${shareData.url}`);
        toast.success('Progress shared to clipboard!');
      }
    } catch (error) {
      console.error('Share error:', error);
      toast.error('Failed to share progress');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20">
      {/* Enhanced Three-Column Layout */}
      <div className="flex min-h-screen">
        {/* Left Quick Actions Sidebar */}
        <motion.div
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-r border-default-200 flex flex-col items-center py-6 space-y-4"
        >
          <div className="text-2xl mb-4">⏱️</div>

          <Button
            size="sm"
            variant="flat"
            onClick={isTracking ? handleStopTracking : handleStartTracking}
            className={`p-3 rounded-lg transition-colors ${
              isTracking
                ? 'hover:bg-danger/10 text-danger'
                : 'hover:bg-success/10 text-success'
            }`}
            title={isTracking ? "Stop Tracking" : "Start Tracking"}
          >
            {isTracking ? <Pause size={16} /> : <Play size={16} />}
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={handleViewAnalytics}
            className="p-3 rounded-lg hover:bg-primary/10 transition-colors"
            title="View Analytics"
          >
            <BarChart3 size={16} />
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={handleViewMissions}
            className="p-3 rounded-lg hover:bg-warning/10 transition-colors"
            title="View Missions"
          >
            <Target size={16} />
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={handleViewProjects}
            className="p-3 rounded-lg hover:bg-secondary/10 transition-colors"
            title="View Projects"
          >
            📁
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/earn')}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="View Earnings"
          >
            💰
          </Button>
        </motion.div>

        {/* Center Content Area */}
        <div className="flex-1 p-6">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            {/* Enhanced Track Page Header */}
            <div className="mb-8">
              <div className="text-center mb-6">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                  Advanced Time Tracking
                </h1>
                <p className="text-lg text-default-600">
                  Track time, collaborate with teams, and analyze productivity with advanced insights
                </p>
              </div>

              {/* Analytics Dashboard */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10">
                  <CardBody className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{loading ? '...' : analytics.todayHours.toFixed(1)}h</div>
                    <div className="text-sm text-default-600">Today</div>
                  </CardBody>
                </Card>
                <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10">
                  <CardBody className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{loading ? '...' : analytics.weekHours.toFixed(1)}h</div>
                    <div className="text-sm text-default-600">This Week</div>
                  </CardBody>
                </Card>
                <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/10">
                  <CardBody className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">{loading ? '...' : analytics.productivity}%</div>
                    <div className="text-sm text-default-600">Productivity</div>
                  </CardBody>
                </Card>
                <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/10">
                  <CardBody className="p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">#{loading ? '...' : (analytics.teamRanking || 'N/A')}</div>
                    <div className="text-sm text-default-600">Team Rank</div>
                  </CardBody>
                </Card>
              </div>

              {/* Control Bar */}
              <div className="flex flex-wrap justify-between items-center gap-4 mb-6">
                <div className="flex items-center gap-3">
                  <Chip
                    color={isTracking ? "success" : "default"}
                    variant="flat"
                    startContent={isTracking ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                  >
                    {isTracking ? "Tracking Active" : "Ready to Track"}
                  </Chip>

                  {projects.length > 0 && (
                    <Dropdown>
                      <DropdownTrigger>
                        <Button variant="flat" size="sm">
                          {selectedProject ? selectedProject.name : 'Select Project'}
                        </Button>
                      </DropdownTrigger>
                      <DropdownMenu
                        onAction={(key) => {
                          const project = projects.find(p => p.id === key);
                          setSelectedProject(project);
                        }}
                      >
                        <DropdownItem key="all">All Projects</DropdownItem>
                        {projects.map(project => (
                          <DropdownItem key={project.id}>{project.name}</DropdownItem>
                        ))}
                      </DropdownMenu>
                    </Dropdown>
                  )}

                  {teamMembers.length > 0 && (
                    <Chip color="secondary" variant="flat" startContent={<Users className="w-4 h-4" />}>
                      {teamMembers.length} Team Members
                    </Chip>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Dropdown>
                    <DropdownTrigger>
                      <Button variant="flat" size="sm" startContent={<Filter className="w-4 h-4" />}>
                        Filter
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu
                      onAction={(key) => {
                        setFilterOptions(prev => ({ ...prev, dateRange: key }));
                      }}
                    >
                      <DropdownItem key="1d">Today</DropdownItem>
                      <DropdownItem key="7d">This Week</DropdownItem>
                      <DropdownItem key="30d">This Month</DropdownItem>
                    </DropdownMenu>
                  </Dropdown>

                  <Dropdown>
                    <DropdownTrigger>
                      <Button variant="flat" size="sm" startContent={<Download className="w-4 h-4" />}>
                        Export
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu onAction={(key) => handleExportData(key)}>
                      <DropdownItem key="csv">Export as CSV</DropdownItem>
                      <DropdownItem key="json">Export as JSON</DropdownItem>
                    </DropdownMenu>
                  </Dropdown>

                  <Button
                    variant="flat"
                    size="sm"
                    startContent={<Share2 className="w-4 h-4" />}
                    onClick={handleShareProgress}
                  >
                    Share
                  </Button>
                </div>
              </div>
            </div>

            {/* Track Tabs */}
            <Card className="mb-6">
              <CardBody className="p-4">
                <Tabs
                  selectedKey={activeTab}
                  onSelectionChange={setActiveTab}
                  variant="underlined"
                  color="primary"
                >
                  <Tab key="time-tracker" title="⏱️ Time Tracker" />
                  <Tab key="quick-submit" title="📤 Quick Submit" />
                  <Tab key="progress" title="📊 Progress" />
                  <Tab key="team-collaboration" title="👥 Team" />
                  <Tab key="analytics" title="📈 Analytics" />
                  <Tab key="recent-activity" title="📋 Activity" />
                </Tabs>
              </CardBody>
            </Card>

            {/* Tab Content */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 'time-tracker' && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <SimpleTimeTracker
                      onTrackingStart={handleStartTracking}
                      onTrackingStop={handleStopTracking}
                      className="h-full"
                    />
                  </div>
                  <div>
                    <ContributionProgress className="h-full" />
                  </div>
                </div>
              )}

              {activeTab === 'quick-submit' && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <QuickContributionForm className="h-full" />
                  </div>
                  <div>
                    <ContributionProgress className="h-full" />
                  </div>
                </div>
              )}

              {activeTab === 'progress' && (
                <div className="grid grid-cols-1 gap-6">
                  <ContributionProgress className="h-full" />
                </div>
              )}

              {activeTab === 'team-collaboration' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardBody className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        Team Members
                      </h3>
                      {teamMembers.length > 0 ? (
                        <div className="space-y-3">
                          {teamMembers.map((member, index) => (
                            <div key={member.user_id} className="flex items-center justify-between p-3 bg-default-100 rounded-lg">
                              <div>
                                <div className="font-medium">
                                  {member.users?.user_metadata?.full_name || member.users?.email || 'Unknown User'}
                                </div>
                                <div className="text-sm text-default-600">{member.role}</div>
                              </div>
                              <Chip size="sm" color="primary" variant="flat">
                                Active
                              </Chip>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-default-500">
                          <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <div className="font-medium">No team members</div>
                          <div className="text-sm">Select a project to see team collaboration</div>
                        </div>
                      )}
                    </CardBody>
                  </Card>

                  <Card>
                    <CardBody className="p-6">
                      <h3 className="text-lg font-semibold mb-4">Team Activity</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                          <div>
                            <div className="font-medium">Team Goal Progress</div>
                            <div className="text-sm text-default-600">Weekly target: 40 hours</div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-green-600">75%</div>
                            <div className="text-xs text-default-500">30h completed</div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                          <div>
                            <div className="font-medium">Active Contributors</div>
                            <div className="text-sm text-default-600">This week</div>
                          </div>
                          <div className="text-lg font-bold text-blue-600">{teamMembers.length}</div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                          <div>
                            <div className="font-medium">Collaboration Score</div>
                            <div className="text-sm text-default-600">Team efficiency</div>
                          </div>
                          <div className="text-lg font-bold text-purple-600">92%</div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </div>
              )}

              {activeTab === 'analytics' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardBody className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <BarChart3 className="w-5 h-5" />
                        Performance Analytics
                      </h3>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between text-sm mb-2">
                            <span>Daily Average</span>
                            <span>{analytics.todayHours.toFixed(1)}h</span>
                          </div>
                          <Progress value={(analytics.todayHours / 8) * 100} color="primary" />
                        </div>

                        <div>
                          <div className="flex justify-between text-sm mb-2">
                            <span>Weekly Progress</span>
                            <span>{analytics.weekHours.toFixed(1)}h / 40h</span>
                          </div>
                          <Progress value={(analytics.weekHours / 40) * 100} color="success" />
                        </div>

                        <div>
                          <div className="flex justify-between text-sm mb-2">
                            <span>Productivity Score</span>
                            <span>{analytics.productivity}%</span>
                          </div>
                          <Progress value={analytics.productivity} color="warning" />
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  <Card>
                    <CardBody className="p-6">
                      <h3 className="text-lg font-semibold mb-4">Insights & Recommendations</h3>
                      <div className="space-y-3">
                        <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                          <div className="font-medium text-blue-800 dark:text-blue-200">💡 Peak Performance</div>
                          <div className="text-sm text-blue-600 dark:text-blue-300">
                            You're most productive between 9-11 AM. Schedule important tasks during this time.
                          </div>
                        </div>

                        <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                          <div className="font-medium text-green-800 dark:text-green-200">🎯 Goal Achievement</div>
                          <div className="text-sm text-green-600 dark:text-green-300">
                            You're on track to exceed your weekly goal by 15%. Great work!
                          </div>
                        </div>

                        <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                          <div className="font-medium text-orange-800 dark:text-orange-200">⚡ Efficiency Tip</div>
                          <div className="text-sm text-orange-600 dark:text-orange-300">
                            Consider taking breaks every 90 minutes to maintain focus and productivity.
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </div>
              )}

              {activeTab === 'recent-activity' && (
                <div className="grid grid-cols-1 gap-6">
                  <Card>
                    <CardBody className="p-6">
                      <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
                      <p className="text-default-600">
                        Recent activity will be displayed here. This integrates with the existing contribution system.
                      </p>
                      <Button
                        color="primary"
                        variant="flat"
                        onClick={handleViewAnalytics}
                        className="mt-4"
                        startContent={<TrendingUp size={16} />}
                      >
                        View Full Analytics
                      </Button>
                    </CardBody>
                  </Card>
                </div>
              )}
            </motion.div>
          </motion.div>
        </div>

        {/* Right Context Sidebar */}
        <motion.div
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-l border-default-200 flex flex-col items-center py-6 space-y-4"
        >
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/validation/metrics')}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="Validation"
          >
            ✅
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/teams')}
            className="p-3 rounded-lg hover:bg-primary/10 transition-colors"
            title="Teams"
          >
            👥
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/profile')}
            className="p-3 rounded-lg hover:bg-secondary/10 transition-colors"
            title="Profile"
          >
            👤
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/settings')}
            className="p-3 rounded-lg hover:bg-default/10 transition-colors"
            title="Settings"
          >
            ⚙️
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default TrackPage;
