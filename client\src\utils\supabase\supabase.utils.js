/**
 * Supabase Client Configuration
 * 
 * Production-ready Supabase client with:
 * - Environment variable validation
 * - Error handling and retry logic
 * - Performance optimization
 * - Security configuration
 */

import { createClient } from '@supabase/supabase-js';

// Environment variables validation with fallbacks for development/testing
let supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
let supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Handle placeholder values from template files
if (!supabaseUrl || supabaseUrl === 'your_supabase_url' || supabaseUrl === 'https://your-project.supabase.co') {
  supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
}

if (!supabaseAnonKey || supabaseAnonKey === 'your_supabase_anon_key' || supabaseAnonKey.length < 50) {
  supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';
}

// Final validation
if (!supabaseUrl || !supabaseUrl.startsWith('https://')) {
  console.error('Invalid VITE_SUPABASE_URL after fallback:', supabaseUrl);
  throw new Error('Invalid or missing VITE_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey || supabaseAnonKey.length < 50) {
  console.error('Invalid VITE_SUPABASE_ANON_KEY after fallback:', supabaseAnonKey ? 'Present but invalid' : 'Missing');
  throw new Error('Invalid or missing VITE_SUPABASE_ANON_KEY environment variable');
}

// Supabase client configuration
const supabaseConfig = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  global: {
    headers: {
      'X-Client-Info': 'royaltea-web'
    }
  },
  db: {
    schema: 'public'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
};

// Debug logging for development
if (import.meta.env.DEV || import.meta.env.VITE_DEBUG === 'true') {
  console.log('🔧 Supabase Configuration:');
  console.log('URL:', supabaseUrl);
  console.log('Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'Missing');
  console.log('Environment:', import.meta.env.NODE_ENV);
}

// Create Supabase client with error handling
let supabase;
try {
  supabase = createClient(supabaseUrl, supabaseAnonKey, supabaseConfig);
  console.log('✅ Supabase client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Supabase client:', error);
  // Create a mock client for development/testing
  supabase = {
    auth: {
      getUser: () => Promise.resolve({ data: { user: null }, error: null }),
      signInWithPassword: () => Promise.resolve({ data: null, error: new Error('Supabase not available') }),
      signUp: () => Promise.resolve({ data: null, error: new Error('Supabase not available') }),
      signOut: () => Promise.resolve({ error: null }),
      resetPasswordForEmail: () => Promise.resolve({ error: null }),
      updateUser: () => Promise.resolve({ data: null, error: new Error('Supabase not available') })
    },
    from: () => ({
      select: () => Promise.resolve({ data: [], error: null }),
      insert: () => Promise.resolve({ data: null, error: new Error('Supabase not available') }),
      update: () => Promise.resolve({ data: null, error: new Error('Supabase not available') }),
      delete: () => Promise.resolve({ data: null, error: new Error('Supabase not available') })
    }),
    storage: {
      from: () => ({
        upload: () => Promise.resolve({ data: null, error: new Error('Supabase not available') }),
        getPublicUrl: () => ({ data: { publicUrl: '' } }),
        remove: () => Promise.resolve({ data: null, error: new Error('Supabase not available') })
      })
    },
    rpc: () => Promise.resolve({ data: null, error: new Error('Supabase not available') }),
    channel: () => ({
      on: () => ({ subscribe: () => ({}) }),
      subscribe: () => ({})
    }),
    removeChannel: () => Promise.resolve()
  };
}

export { supabase };

// Helper functions for common operations
export const supabaseHelpers = {
  /**
   * Get current user
   */
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;
      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  /**
   * Sign in with email and password
   */
  async signIn(email, password) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  },

  /**
   * Sign up with email and password
   */
  async signUp(email, password, metadata = {}) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata
        }
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  },

  /**
   * Sign out
   */
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  },

  /**
   * Reset password
   */
  async resetPassword(email) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      if (error) throw error;
    } catch (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
  },

  /**
   * Update user profile
   */
  async updateProfile(updates) {
    try {
      const { data, error } = await supabase.auth.updateUser({
        data: updates
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },

  /**
   * Upload file to storage
   */
  async uploadFile(bucket, path, file, options = {}) {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: '3600',
          upsert: false,
          ...options
        });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  },

  /**
   * Get public URL for file
   */
  getPublicUrl(bucket, path) {
    try {
      const { data } = supabase.storage
        .from(bucket)
        .getPublicUrl(path);
      return data.publicUrl;
    } catch (error) {
      console.error('Error getting public URL:', error);
      return null;
    }
  },

  /**
   * Delete file from storage
   */
  async deleteFile(bucket, paths) {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .remove(Array.isArray(paths) ? paths : [paths]);
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  },

  /**
   * Execute RPC function
   */
  async rpc(functionName, params = {}) {
    try {
      const { data, error } = await supabase.rpc(functionName, params);
      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error executing RPC ${functionName}:`, error);
      throw error;
    }
  },

  /**
   * Subscribe to real-time changes
   */
  subscribe(table, callback, filter = '*') {
    try {
      const subscription = supabase
        .channel(`${table}-changes`)
        .on('postgres_changes', {
          event: filter,
          schema: 'public',
          table: table
        }, callback)
        .subscribe();

      return subscription;
    } catch (error) {
      console.error('Error subscribing to changes:', error);
      return null;
    }
  },

  /**
   * Unsubscribe from real-time changes
   */
  async unsubscribe(subscription) {
    try {
      if (subscription) {
        await supabase.removeChannel(subscription);
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
    }
  }
};

// Export default client
export default supabase;
